import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  RefreshCw, 
  Shield, 
  TrendingUp, 
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Activity,
  Zap
} from 'lucide-react';
import { recoveryApi } from '@/api/client';
import { ComponentType, RecoveryStats, FallbackStats } from '@/types/api';
import { getSourceColor } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface RecoveryDashboardProps {
  refreshInterval?: number;
  showRefreshButton?: boolean;
}

const RecoveryDashboard: React.FC<RecoveryDashboardProps> = ({ 
  refreshInterval = 30000, // 30 seconds
  showRefreshButton = true 
}) => {
  const [recoveryStats, setRecoveryStats] = useState<Record<ComponentType, RecoveryStats>>({} as Record<ComponentType, RecoveryStats>);
  const [fallbackStats, setFallbackStats] = useState<FallbackStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const { toast } = useToast();

  const fetchRecoveryData = async () => {
    try {
      setError(null);
      const [recoveryData, fallbackData] = await Promise.all([
        recoveryApi.getRecoveryCapabilities(),
        recoveryApi.getFallbackStats()
      ]);
      
      setRecoveryStats(recoveryData);
      setFallbackStats(fallbackData);
      setLastUpdated(new Date());
    } catch (err: any) {
      setError(err.message || 'Failed to fetch recovery data');
      toast({
        title: "Recovery Data Fetch Failed",
        description: "Failed to fetch recovery and fallback statistics",
        variant: "destructive",
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRecoveryData();
    
    if (refreshInterval > 0) {
      const interval = setInterval(fetchRecoveryData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshInterval]);

  const getComponentDisplayName = (component: ComponentType): string => {
    const displayNames: Record<ComponentType, string> = {
      rule_engine: 'Rule Engine',
      cache: 'Cache',
      llm: 'LLM',
      clarifier: 'Clarifier'
    };
    return displayNames[component] || component;
  };

  const getRecoveryRateColor = (rate: number) => {
    if (rate >= 90) return 'text-green-600';
    if (rate >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getRecoveryStatusIcon = (rate: number) => {
    if (rate >= 90) return <CheckCircle className="w-4 h-4 text-green-500" />;
    if (rate >= 70) return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    return <XCircle className="w-4 h-4 text-red-500" />;
  };

  const renderRecoveryCard = (component: ComponentType, stats: RecoveryStats) => {
    const componentColor = getSourceColor(component);
    
    return (
      <Card key={component} className="transition-all duration-200 hover:shadow-md">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">
              {getComponentDisplayName(component)}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge className={componentColor} variant="outline">
                {component}
              </Badge>
              {getRecoveryStatusIcon(stats.recovery_success_rate)}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Recovery Success Rate */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Shield className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">Recovery Rate</span>
            </div>
            <Badge 
              variant="outline" 
              className={getRecoveryRateColor(stats.recovery_success_rate)}
            >
              {stats.recovery_success_rate.toFixed(1)}%
            </Badge>
          </div>

          {/* Recovery Attempts */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Activity className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">Attempts</span>
            </div>
            <Badge variant="outline" className="text-xs">
              {stats.recovery_attempts.toLocaleString()}
            </Badge>
          </div>

          {/* Successful Recoveries */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">Successes</span>
            </div>
            <Badge 
              variant="outline" 
              className={stats.recovery_successes > 0 ? 'text-green-600' : 'text-gray-600'}
            >
              {stats.recovery_successes.toLocaleString()}
            </Badge>
          </div>

          {/* Average Recovery Time */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">Avg Time</span>
            </div>
            <Badge 
              variant="outline" 
              className={stats.avg_recovery_time_ms < 1000 ? 'text-green-600' : 'text-yellow-600'}
            >
              {stats.avg_recovery_time_ms.toFixed(2)}ms
            </Badge>
          </div>

          {/* Last Recovery Attempt */}
          <div className="text-xs text-gray-500 flex items-center justify-between">
            <span>Last Attempt:</span>
            <span className="font-medium">
              {stats.last_recovery_attempt ? 
                new Date(stats.last_recovery_attempt).toLocaleTimeString() : 
                'Never'
              }
            </span>
          </div>
        </CardContent>
      </Card>
    );
  };

  const getOverallRecoveryStats = () => {
    const components = Object.values(recoveryStats);
    if (components.length === 0) return { avgRate: 0, totalAttempts: 0, totalSuccesses: 0 };

    const avgRate = components.reduce((sum, comp) => sum + comp.recovery_success_rate, 0) / components.length;
    const totalAttempts = components.reduce((sum, comp) => sum + comp.recovery_attempts, 0);
    const totalSuccesses = components.reduce((sum, comp) => sum + comp.recovery_successes, 0);

    return { avgRate, totalAttempts, totalSuccesses };
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Recovery Dashboard</h3>
          <Skeleton className="h-9 w-24" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-3">
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent className="space-y-3">
                {[...Array(4)].map((_, j) => (
                  <div key={j} className="flex justify-between">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-12" />
                  </div>
                ))}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const overallStats = getOverallRecoveryStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Recovery Dashboard</h3>
          {lastUpdated && (
            <p className="text-sm text-gray-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </p>
          )}
        </div>
        
        {showRefreshButton && (
          <Button
            variant="outline"
            size="sm"
            onClick={fetchRecoveryData}
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        )}
      </div>

      {/* Overall Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Recovery Overview */}
        <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Shield className="w-5 h-5 text-green-600" />
              Recovery Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-700">
                  {overallStats.avgRate.toFixed(1)}%
                </div>
                <div className="text-sm text-green-600">Avg Success Rate</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-700">
                  {overallStats.totalSuccesses}/{overallStats.totalAttempts}
                </div>
                <div className="text-sm text-blue-600">Success/Attempts</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Fallback Stats */}
        {fallbackStats && (
          <Card className="bg-gradient-to-r from-orange-50 to-red-50 border-orange-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Zap className="w-5 h-5 text-orange-600" />
                Fallback Statistics
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-700">
                    {fallbackStats.total_fallbacks.toLocaleString()}
                  </div>
                  <div className="text-sm text-orange-600">Total Fallbacks</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-700">
                    {fallbackStats.fallback_rate.toFixed(2)}%
                  </div>
                  <div className="text-sm text-red-600">Fallback Rate</div>
                </div>
              </div>
              
              {/* Common Fallback Triggers */}
              {fallbackStats.common_fallback_triggers && fallbackStats.common_fallback_triggers.length > 0 && (
                <div className="mt-3">
                  <div className="text-sm font-medium text-gray-700 mb-2">Common Triggers:</div>
                  <div className="space-y-1">
                    {fallbackStats.common_fallback_triggers.slice(0, 3).map((trigger, index) => (
                      <div key={index} className="text-xs bg-orange-100 p-1 rounded text-orange-800">
                        {trigger}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-2">
              <XCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-red-800">Recovery Data Error</p>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Component Recovery Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Object.entries(recoveryStats).map(([component, stats]) => 
          renderRecoveryCard(component as ComponentType, stats)
        )}
      </div>
    </div>
  );
};

export default RecoveryDashboard;
