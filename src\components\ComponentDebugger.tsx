import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Play, 
  Loader2, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Code,
  Eye,
  EyeOff
} from 'lucide-react';
import { componentDebugApi } from '@/api/client';
import { ComponentType, ComponentDebugRequest, ComponentDebugResponse } from '@/types/api';
import { getSourceColor } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface ComponentDebuggerProps {
  defaultComponent?: ComponentType;
  defaultMessage?: string;
}

const ComponentDebugger: React.FC<ComponentDebuggerProps> = ({ 
  defaultComponent = 'rule_engine',
  defaultMessage = 'Hello, I need help with my order'
}) => {
  const [selectedComponent, setSelectedComponent] = useState<ComponentType>(defaultComponent);
  const [testMessage, setTestMessage] = useState(defaultMessage);
  const [debugLevel, setDebugLevel] = useState<'basic' | 'detailed' | 'verbose'>('basic');
  const [debugResult, setDebugResult] = useState<ComponentDebugResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showRawJson, setShowRawJson] = useState(false);
  const { toast } = useToast();

  const componentOptions: { value: ComponentType; label: string; description: string }[] = [
    { 
      value: 'rule_engine', 
      label: 'Rule Engine', 
      description: 'Test rule matching and pattern recognition' 
    },
    { 
      value: 'cache', 
      label: 'Cache', 
      description: 'Test cache hit/miss behavior' 
    },
    { 
      value: 'llm', 
      label: 'LLM', 
      description: 'Test language model processing' 
    },
    { 
      value: 'clarifier', 
      label: 'Clarifier', 
      description: 'Test clarification logic' 
    },
  ];

  const debugLevelOptions = [
    { value: 'basic', label: 'Basic', description: 'Essential debug information' },
    { value: 'detailed', label: 'Detailed', description: 'Comprehensive debug data' },
    { value: 'verbose', label: 'Verbose', description: 'Full debug trace' },
  ];

  const handleDebugComponent = async () => {
    if (!testMessage.trim()) {
      toast({
        title: "Message Required",
        description: "Please enter a test message",
        variant: "destructive",
        duration: 3000,
      });
      return;
    }

    setIsLoading(true);
    setError(null);
    setDebugResult(null);

    try {
      const request: ComponentDebugRequest = {
        component: selectedComponent,
        message: testMessage.trim(),
        debug_level: debugLevel,
      };

      const result = await componentDebugApi.debugComponent(request);
      setDebugResult(result);
      
      toast({
        title: "Debug Complete",
        description: `${selectedComponent} debugging completed successfully`,
        duration: 3000,
      });
    } catch (err: any) {
      setError(err.message || 'Failed to debug component');
      toast({
        title: "Debug Failed",
        description: `Failed to debug ${selectedComponent}`,
        variant: "destructive",
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    }
  };

  const getComponentDisplayName = (component: ComponentType): string => {
    const option = componentOptions.find(opt => opt.value === component);
    return option?.label || component;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold">Component Debugger</h3>
        <p className="text-sm text-gray-600">
          Test individual components in isolation to debug specific issues
        </p>
      </div>

      {/* Debug Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Debug Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Component Selection */}
          <div className="space-y-2">
            <Label htmlFor="component-select">Component</Label>
            <Select 
              value={selectedComponent} 
              onValueChange={(value) => setSelectedComponent(value as ComponentType)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select component to debug" />
              </SelectTrigger>
              <SelectContent>
                {componentOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <Badge className={getSourceColor(option.value)} variant="outline">
                        {option.label}
                      </Badge>
                      <span className="text-sm text-gray-600">{option.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Debug Level */}
          <div className="space-y-2">
            <Label htmlFor="debug-level">Debug Level</Label>
            <Select 
              value={debugLevel} 
              onValueChange={(value) => setDebugLevel(value as 'basic' | 'detailed' | 'verbose')}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select debug level" />
              </SelectTrigger>
              <SelectContent>
                {debugLevelOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div>
                      <div className="font-medium">{option.label}</div>
                      <div className="text-xs text-gray-500">{option.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Test Message */}
          <div className="space-y-2">
            <Label htmlFor="test-message">Test Message</Label>
            <Textarea
              id="test-message"
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
              placeholder="Enter a test message to debug..."
              className="min-h-[80px]"
              disabled={isLoading}
            />
          </div>

          {/* Debug Button */}
          <Button
            onClick={handleDebugComponent}
            disabled={!testMessage.trim() || isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Debugging {getComponentDisplayName(selectedComponent)}...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                Debug {getComponentDisplayName(selectedComponent)}
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-2">
              <XCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-red-800">Debug Error</p>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Debug Results */}
      {debugResult && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-base flex items-center gap-2">
                {getStatusIcon(debugResult.status)}
                Debug Results - {getComponentDisplayName(debugResult.component)}
              </CardTitle>
              <Badge className={getSourceColor(debugResult.component)}>
                {debugResult.component}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Processing Time */}
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Processing Time</span>
              <Badge variant="outline">
                {debugResult.processing_time_ms.toFixed(5)}ms
              </Badge>
            </div>

            {/* Status */}
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Status</span>
              <Badge 
                variant={debugResult.status === 'success' ? 'default' : 'destructive'}
              >
                {debugResult.status}
              </Badge>
            </div>

            {/* Errors */}
            {debugResult.errors && debugResult.errors.length > 0 && (
              <div className="space-y-2">
                <span className="text-sm text-gray-600">Errors</span>
                <div className="space-y-1">
                  {debugResult.errors.map((error, index) => (
                    <div key={index} className="text-sm text-red-600 bg-red-50 p-2 rounded">
                      {error}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Warnings */}
            {debugResult.warnings && debugResult.warnings.length > 0 && (
              <div className="space-y-2">
                <span className="text-sm text-gray-600">Warnings</span>
                <div className="space-y-1">
                  {debugResult.warnings.map((warning, index) => (
                    <div key={index} className="text-sm text-yellow-600 bg-yellow-50 p-2 rounded">
                      {warning}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Debug Info */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Debug Information</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowRawJson(!showRawJson)}
                >
                  {showRawJson ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  {showRawJson ? 'Hide' : 'Show'} Raw JSON
                </Button>
              </div>
              
              {showRawJson ? (
                <pre className="text-xs bg-gray-100 p-3 rounded overflow-auto max-h-64">
                  {JSON.stringify(debugResult.debug_info, null, 2)}
                </pre>
              ) : (
                <div className="bg-gray-50 p-3 rounded">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                    {Object.entries(debugResult.debug_info).map(([key, value]) => (
                      <div key={key} className="flex justify-between">
                        <span className="text-gray-600 capitalize">
                          {key.replace(/_/g, ' ')}:
                        </span>
                        <span className="font-medium">
                          {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ComponentDebugger;
