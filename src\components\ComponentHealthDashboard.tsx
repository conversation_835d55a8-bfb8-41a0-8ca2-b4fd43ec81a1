import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  RefreshCw,
  Activity,
  Clock,
  TrendingUp,
  AlertCircle
} from 'lucide-react';
import { componentHealthApi } from '@/api/client';
import { ComponentHealthResponse, ComponentHealth, ComponentType } from '@/types/api';
import { getSourceColor } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface ComponentHealthDashboardProps {
  refreshInterval?: number;
  showRefreshButton?: boolean;
}

const ComponentHealthDashboard: React.FC<ComponentHealthDashboardProps> = ({ 
  refreshInterval = 30000, // 30 seconds
  showRefreshButton = true 
}) => {
  const [componentHealth, setComponentHealth] = useState<ComponentHealthResponse>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const { toast } = useToast();

  const fetchComponentHealth = async () => {
    try {
      setError(null);
      const healthData = await componentHealthApi.getComponentsHealth();
      setComponentHealth(healthData);
      setLastUpdated(new Date());
    } catch (err: any) {
      setError(err.message || 'Failed to fetch component health');
      toast({
        title: "Health Check Failed",
        description: "Failed to fetch component health data",
        variant: "destructive",
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchComponentHealth();
    
    if (refreshInterval > 0) {
      const interval = setInterval(fetchComponentHealth, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshInterval]);

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-400" />;
    }
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getComponentDisplayName = (component: string): string => {
    const displayNames: Record<string, string> = {
      rule_engine: 'Rule Engine',
      cache: 'Cache',
      llm: 'LLM',
      clarifier: 'Clarifier'
    };
    return displayNames[component] || component;
  };

  const renderHealthCard = (component: string, health: ComponentHealth) => {
    return (
      <Card key={component} className={`transition-all duration-200 hover:shadow-md ${getHealthColor(health.status)}`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">
              {getComponentDisplayName(component)}
            </CardTitle>
            {getHealthIcon(health.status)}
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Health Score */}
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Health Score</span>
            <Badge 
              variant="outline" 
              className={`font-semibold ${
                health.health_score >= 90 ? 'text-green-600' : 
                health.health_score >= 70 ? 'text-yellow-600' : 'text-red-600'
              }`}
            >
              {health.health_score}/100
            </Badge>
          </div>

          {/* Recent Errors */}
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Recent Errors</span>
            <Badge 
              variant={health.recent_errors > 0 ? "destructive" : "outline"}
              className="text-xs"
            >
              {health.recent_errors}
            </Badge>
          </div>

          {/* Uptime */}
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Uptime</span>
            <Badge variant="outline" className="text-xs">
              {health.uptime_percentage.toFixed(2)}%
            </Badge>
          </div>

          {/* Response Time */}
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Response Time</span>
            <Badge 
              variant="outline" 
              className={`text-xs ${
                health.response_time_ms < 100 ? 'text-green-600' : 
                health.response_time_ms < 500 ? 'text-yellow-600' : 'text-red-600'
              }`}
            >
              {health.response_time_ms.toFixed(2)}ms
            </Badge>
          </div>

          {/* Last Check */}
          <div className="text-xs text-gray-500 flex items-center gap-1">
            <Clock className="w-3 h-3" />
            Last check: {new Date(health.last_check).toLocaleTimeString()}
          </div>
        </CardContent>
      </Card>
    );
  };

  const getOverallHealth = () => {
    const components = Object.values(componentHealth);
    if (components.length === 0) return { status: 'unknown', score: 0 };

    const avgScore = components.reduce((sum, comp) => sum + comp.health_score, 0) / components.length;
    const hasErrors = components.some(comp => comp.status === 'error');
    const hasWarnings = components.some(comp => comp.status === 'warning');

    let status = 'healthy';
    if (hasErrors) status = 'error';
    else if (hasWarnings) status = 'warning';

    return { status, score: Math.round(avgScore) };
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Component Health Dashboard</h3>
          <Skeleton className="h-9 w-24" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-3">
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent className="space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const overallHealth = getOverallHealth();
  const componentCount = Object.keys(componentHealth).length;
  const healthyCount = Object.values(componentHealth).filter(comp => comp.status === 'healthy').length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Component Health Dashboard</h3>
          {lastUpdated && (
            <p className="text-sm text-gray-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </p>
          )}
        </div>
        
        {showRefreshButton && (
          <Button
            variant="outline"
            size="sm"
            onClick={fetchComponentHealth}
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        )}
      </div>

      {/* Overall Health Summary */}
      <Card className={getHealthColor(overallHealth.status)}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getHealthIcon(overallHealth.status)}
              <div>
                <h4 className="font-semibold">System Health: {overallHealth.score}/100</h4>
                <p className="text-sm text-gray-600">
                  {healthyCount}/{componentCount} Components Healthy
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-gray-500" />
              <TrendingUp className="w-5 h-5 text-green-500" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-2">
              <XCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-red-800">Health Check Error</p>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Component Health Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Object.entries(componentHealth).map(([component, health]) => 
          renderHealthCard(component, health)
        )}
      </div>
    </div>
  );
};

export default ComponentHealthDashboard;
