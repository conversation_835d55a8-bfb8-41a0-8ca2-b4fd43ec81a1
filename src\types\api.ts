// API Types for AI Agent Debug Panel

export interface IntentParseRequest {
  message: string;
  user_id: string;
}

export interface IntentParseResponse {
  intent: string;
  confidence: number;
  entities: Record<string, any>;
  source: 'rule' | 'cache' | 'llm' | 'clarifier';
  source_chain: string;
  processing_time_ms: number;
  rule_id?: string;
  rule_source?: string;
  pattern_matched?: string;
  session_id: string | null;
  tenant_id: string | null;
  tenant_metadata?: {
    vertical: string;
    custom_intents_count: number;
    has_custom_rules: boolean;
  };
  clarification_needed: boolean;
  processing_metadata?: {
    message_length: number;
    tenant_id: string;
    session_id: string | null;
    locale: string;
    processing_time_ms: number;
    source_chain: string[];
    final_source: string;
    rule_check_time_ms: number;
    cache_check_time_ms: number;
    llm_call_time_ms: number;
    clarifier_time_ms: number;
    rule_matched: boolean;
    cache_hit: boolean;
    llm_called: boolean;
    clarifier_triggered: boolean;
    errors: string[];
    warnings: string[];
    tenant_vertical: string;
    custom_intents_count: number;
  };
}

export interface SalesAgentRequest {
  text: string;
  user_id: string;
}

export interface SalesAgentResponse {
  status: 'success' | 'error';
  content: {
    status: 'success' | 'error';
    content: string;
    metadata: {
      source: string;
      tenant_id: string;
      user_id: string;
      detected_intent: string;
      retrieval_strategy: string;
      session_context: {
        clarifier_rounds: number;
      };
      integration_components: string[];
    };
  };
  metadata: Record<string, any>;
}

export interface DebugInfo {
  request_status: 'idle' | 'loading' | 'success' | 'error';
  response_time: number;
  parsed_intent?: string;
  confidence_score?: number;
  source_chain?: string;
  tool_execution_status?: 'success' | 'error' | 'pending';
  cache_status?: 'hit' | 'miss';
  fallback_used?: boolean;
  processing_latency?: number;
  raw_response?: Record<string, any>;
  error_message?: string;
}

export interface TestSession {
  id: string;
  created_at: Date;
  messages: ChatMessage[];
}

export interface ChatMessage {
  id: string;
  type: 'user' | 'agent' | 'system';
  content: string | IntentParseResponse | SalesAgentResponse;
  timestamp: Date;
  debug_info?: DebugInfo;
  request_id?: string;
  feedback?: {
    helpful: boolean | null;
    comment?: string;
  };
}

// Connection status types
export interface ConnectionStatus {
  status: 'connected' | 'disconnected' | 'checking';
  last_ping: Date | null;
  latency: number | null;
}

// API Error types
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

// Component-specific types for new backend integration
export type ComponentType = 'rule_engine' | 'cache' | 'llm' | 'clarifier';

// Component Health Monitoring
export interface ComponentHealth {
  status: 'healthy' | 'warning' | 'error';
  health_score: number;
  recent_errors: number;
  last_check: string;
  uptime_percentage: number;
  response_time_ms: number;
}

export interface ComponentHealthResponse {
  [key: string]: ComponentHealth;
}

// Component-specific Error Tracking
export interface ComponentError {
  error_id: string;
  component: ComponentType;
  error_type: string;
  message: string;
  timestamp: string;
  frequency: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: Record<string, any>;
}

export interface ComponentErrorResponse {
  component: ComponentType;
  total_errors: number;
  recent_errors: ComponentError[];
  common_patterns: string[];
  error_rate: number;
}

// Component Performance Metrics
export interface ComponentPerformance {
  avg_time_ms: number;
  success_rate: number;
  error_rate: number;
  total_requests: number;
  requests_per_minute: number;
  p95_response_time: number;
  p99_response_time: number;
}

export interface ComponentPerformanceResponse {
  [key: string]: ComponentPerformance;
}

// Recovery and Fallback Tracking
export interface RecoveryStats {
  recovery_attempts: number;
  recovery_successes: number;
  recovery_success_rate: number;
  avg_recovery_time_ms: number;
  last_recovery_attempt: string;
}

export interface FallbackStats {
  total_fallbacks: number;
  fallback_rate: number;
  fallback_success_rate: number;
  common_fallback_triggers: string[];
}

// Component Debug Request
export interface ComponentDebugRequest {
  component: ComponentType;
  message: string;
  tenant_id?: string;
  debug_level?: 'basic' | 'detailed' | 'verbose';
}

export interface ComponentDebugResponse {
  component: ComponentType;
  status: 'success' | 'error';
  processing_time_ms: number;
  debug_info: Record<string, any>;
  errors?: string[];
  warnings?: string[];
}
