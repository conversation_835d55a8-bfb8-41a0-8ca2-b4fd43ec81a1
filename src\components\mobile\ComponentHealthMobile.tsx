import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  RefreshCw,
  ChevronRight,
  Activity,
  Clock,
  AlertCircle
} from 'lucide-react';
import { componentHealthApi } from '@/api/client';
import { ComponentHealthResponse, ComponentHealth, ComponentType } from '@/types/api';
import { useToast } from '@/hooks/use-toast';

interface ComponentHealthMobileProps {
  refreshInterval?: number;
  autoRefresh?: boolean;
}

const ComponentHealthMobile: React.FC<ComponentHealthMobileProps> = ({ 
  refreshInterval = 30000, // 30 seconds
  autoRefresh = true
}) => {
  const [componentHealth, setComponentHealth] = useState<ComponentHealthResponse>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [expandedComponent, setExpandedComponent] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchComponentHealth = async () => {
    try {
      setError(null);
      const healthData = await componentHealthApi.getComponentsHealth();
      setComponentHealth(healthData);
      setLastUpdated(new Date());
    } catch (err: any) {
      setError(err.message || 'Failed to fetch component health');
      toast({
        title: "Health Check Failed",
        description: "Failed to fetch component health data",
        variant: "destructive",
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchComponentHealth();
    
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(fetchComponentHealth, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshInterval, autoRefresh]);

  const getHealthIcon = (status: string, size: 'sm' | 'lg' = 'sm') => {
    const iconClass = size === 'lg' ? 'w-6 h-6' : 'w-4 h-4';
    
    switch (status) {
      case 'healthy':
        return <CheckCircle className={`${iconClass} text-green-500`} />;
      case 'warning':
        return <AlertTriangle className={`${iconClass} text-yellow-500`} />;
      case 'error':
        return <XCircle className={`${iconClass} text-red-500`} />;
      default:
        return <AlertCircle className={`${iconClass} text-gray-400`} />;
    }
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-50 border-green-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const getComponentDisplayName = (component: string): string => {
    const displayNames: Record<string, string> = {
      rule_engine: 'Rule Engine',
      cache: 'Cache',
      llm: 'LLM',
      clarifier: 'Clarifier'
    };
    return displayNames[component] || component;
  };

  const getOverallHealth = () => {
    const components = Object.values(componentHealth);
    if (components.length === 0) return { status: 'unknown', score: 0, healthyCount: 0, totalCount: 0 };

    const avgScore = components.reduce((sum, comp) => sum + comp.health_score, 0) / components.length;
    const hasErrors = components.some(comp => comp.status === 'error');
    const hasWarnings = components.some(comp => comp.status === 'warning');
    const healthyCount = components.filter(comp => comp.status === 'healthy').length;

    let status = 'healthy';
    if (hasErrors) status = 'error';
    else if (hasWarnings) status = 'warning';

    return { 
      status, 
      score: Math.round(avgScore), 
      healthyCount, 
      totalCount: components.length 
    };
  };

  const toggleComponentExpansion = (component: string) => {
    setExpandedComponent(expandedComponent === component ? null : component);
  };

  if (isLoading) {
    return (
      <div className="space-y-4 p-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">System Health</h3>
          <Skeleton className="h-8 w-20" />
        </div>
        
        <Card className="animate-pulse">
          <CardContent className="p-4">
            <Skeleton className="h-16 w-full" />
          </CardContent>
        </Card>
        
        <div className="space-y-2">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-3">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-6 w-16" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const overallHealth = getOverallHealth();

  return (
    <div className="space-y-4 p-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">System Health</h3>
          {lastUpdated && (
            <p className="text-xs text-gray-500">
              Updated: {lastUpdated.toLocaleTimeString()}
            </p>
          )}
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={fetchComponentHealth}
          disabled={isLoading}
        >
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      {/* Overall Health Summary */}
      <Card className={`${getHealthColor(overallHealth.status)} transition-all duration-200`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getHealthIcon(overallHealth.status, 'lg')}
              <div>
                <div className="font-semibold text-lg">
                  System Health: {overallHealth.score}/100
                </div>
                <div className="text-sm text-gray-600">
                  {overallHealth.healthyCount}/{overallHealth.totalCount} Components Healthy
                </div>
              </div>
            </div>
            <div className="text-right">
              <Activity className="w-5 h-5 text-gray-500 mx-auto mb-1" />
              <div className="text-xs text-gray-500">Live</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-3">
            <div className="flex items-start gap-2">
              <XCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-red-800">Health Check Error</p>
                <p className="text-xs text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Component List */}
      <div className="space-y-2">
        {Object.entries(componentHealth).map(([component, health]) => (
          <Card 
            key={component} 
            className={`${getHealthColor(health.status)} transition-all duration-200 cursor-pointer hover:shadow-md`}
            onClick={() => toggleComponentExpansion(component)}
          >
            <CardContent className="p-3">
              {/* Component Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getHealthIcon(health.status)}
                  <div>
                    <div className="font-medium text-sm">
                      {getComponentDisplayName(component)}
                    </div>
                    <div className="text-xs text-gray-600">
                      {health.health_score}/100 • {health.recent_errors} errors
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge 
                    variant="outline" 
                    className={`text-xs ${
                      health.health_score >= 90 ? 'text-green-600' : 
                      health.health_score >= 70 ? 'text-yellow-600' : 'text-red-600'
                    }`}
                  >
                    {health.health_score}
                  </Badge>
                  <ChevronRight 
                    className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
                      expandedComponent === component ? 'rotate-90' : ''
                    }`} 
                  />
                </div>
              </div>

              {/* Expanded Details */}
              {expandedComponent === component && (
                <div className="mt-3 pt-3 border-t border-gray-200 space-y-2">
                  <div className="grid grid-cols-2 gap-3 text-xs">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Uptime:</span>
                      <span className="font-medium">{health.uptime_percentage.toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Response:</span>
                      <span className="font-medium">{health.response_time_ms.toFixed(1)}ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Errors:</span>
                      <span className={`font-medium ${health.recent_errors > 0 ? 'text-red-600' : 'text-green-600'}`}>
                        {health.recent_errors}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${
                          health.status === 'healthy' ? 'text-green-600' : 
                          health.status === 'warning' ? 'text-yellow-600' : 'text-red-600'
                        }`}
                      >
                        {health.status}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-1 text-xs text-gray-500 mt-2">
                    <Clock className="w-3 h-3" />
                    <span>Last check: {new Date(health.last_check).toLocaleTimeString()}</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="flex gap-2 pt-2">
        <Button 
          variant="outline" 
          size="sm" 
          className="flex-1"
          onClick={fetchComponentHealth}
          disabled={isLoading}
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh All
        </Button>
        <Button 
          variant="outline" 
          size="sm" 
          className="flex-1"
          onClick={() => {
            const hasExpanded = expandedComponent !== null;
            if (hasExpanded) {
              setExpandedComponent(null);
            } else {
              // Expand the first component with issues, or the first one
              const componentWithIssues = Object.entries(componentHealth).find(
                ([_, health]) => health.status !== 'healthy'
              );
              const firstComponent = Object.keys(componentHealth)[0];
              setExpandedComponent(componentWithIssues?.[0] || firstComponent);
            }
          }}
        >
          {expandedComponent ? 'Collapse' : 'Expand'} Details
        </Button>
      </div>
    </div>
  );
};

export default ComponentHealthMobile;
