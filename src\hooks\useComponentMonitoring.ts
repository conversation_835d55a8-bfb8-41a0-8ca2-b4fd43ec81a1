import { useState, useEffect, useCallback } from 'react';
import { 
  componentHealthApi, 
  componentPerformance<PERSON>pi, 
  componentErrorApi, 
  recoveryApi 
} from '@/api/client';
import { 
  ComponentType, 
  ComponentHealthResponse, 
  ComponentPerformanceResponse, 
  ComponentErrorResponse, 
  RecoveryStats, 
  FallbackStats 
} from '@/types/api';

interface ComponentMonitoringState {
  health: ComponentHealthResponse;
  performance: ComponentPerformanceResponse;
  errors: Record<ComponentType, ComponentErrorResponse>;
  recovery: Record<ComponentType, RecoveryStats>;
  fallback: FallbackStats | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

interface UseComponentMonitoringOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  enableHealthMonitoring?: boolean;
  enablePerformanceMonitoring?: boolean;
  enableErrorTracking?: boolean;
  enableRecoveryTracking?: boolean;
}

export const useComponentMonitoring = (options: UseComponentMonitoringOptions = {}) => {
  const {
    autoRefresh = true,
    refreshInterval = 30000, // 30 seconds
    enableHealthMonitoring = true,
    enablePerformanceMonitoring = true,
    enableErrorTracking = true,
    enableRecoveryTracking = true,
  } = options;

  const [state, setState] = useState<ComponentMonitoringState>({
    health: {},
    performance: {},
    errors: {} as Record<ComponentType, ComponentErrorResponse>,
    recovery: {} as Record<ComponentType, RecoveryStats>,
    fallback: null,
    isLoading: true,
    error: null,
    lastUpdated: null,
  });

  const fetchAllData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, error: null }));

      const promises: Promise<any>[] = [];
      
      if (enableHealthMonitoring) {
        promises.push(componentHealthApi.getComponentsHealth());
      }
      
      if (enablePerformanceMonitoring) {
        promises.push(componentPerformanceApi.getComponentsPerformance());
      }
      
      if (enableErrorTracking) {
        promises.push(componentErrorApi.getAllComponentErrors());
      }
      
      if (enableRecoveryTracking) {
        promises.push(recoveryApi.getRecoveryCapabilities());
        promises.push(recoveryApi.getFallbackStats());
      }

      const results = await Promise.allSettled(promises);
      let resultIndex = 0;

      const newState: Partial<ComponentMonitoringState> = {};

      if (enableHealthMonitoring) {
        const healthResult = results[resultIndex++];
        if (healthResult.status === 'fulfilled') {
          newState.health = healthResult.value;
        }
      }

      if (enablePerformanceMonitoring) {
        const performanceResult = results[resultIndex++];
        if (performanceResult.status === 'fulfilled') {
          newState.performance = performanceResult.value;
        }
      }

      if (enableErrorTracking) {
        const errorResult = results[resultIndex++];
        if (errorResult.status === 'fulfilled') {
          newState.errors = errorResult.value;
        }
      }

      if (enableRecoveryTracking) {
        const recoveryResult = results[resultIndex++];
        const fallbackResult = results[resultIndex++];
        
        if (recoveryResult.status === 'fulfilled') {
          newState.recovery = recoveryResult.value;
        }
        
        if (fallbackResult.status === 'fulfilled') {
          newState.fallback = fallbackResult.value;
        }
      }

      setState(prev => ({
        ...prev,
        ...newState,
        lastUpdated: new Date(),
        isLoading: false,
      }));

    } catch (err: any) {
      setState(prev => ({
        ...prev,
        error: err.message || 'Failed to fetch monitoring data',
        isLoading: false,
      }));
    }
  }, [enableHealthMonitoring, enablePerformanceMonitoring, enableErrorTracking, enableRecoveryTracking]);

  const fetchHealthData = useCallback(async () => {
    if (!enableHealthMonitoring) return;
    
    try {
      const healthData = await componentHealthApi.getComponentsHealth();
      setState(prev => ({
        ...prev,
        health: healthData,
        lastUpdated: new Date(),
      }));
    } catch (err: any) {
      setState(prev => ({
        ...prev,
        error: err.message || 'Failed to fetch health data',
      }));
    }
  }, [enableHealthMonitoring]);

  const fetchPerformanceData = useCallback(async () => {
    if (!enablePerformanceMonitoring) return;
    
    try {
      const performanceData = await componentPerformanceApi.getComponentsPerformance();
      setState(prev => ({
        ...prev,
        performance: performanceData,
        lastUpdated: new Date(),
      }));
    } catch (err: any) {
      setState(prev => ({
        ...prev,
        error: err.message || 'Failed to fetch performance data',
      }));
    }
  }, [enablePerformanceMonitoring]);

  const fetchErrorData = useCallback(async () => {
    if (!enableErrorTracking) return;
    
    try {
      const errorData = await componentErrorApi.getAllComponentErrors();
      setState(prev => ({
        ...prev,
        errors: errorData,
        lastUpdated: new Date(),
      }));
    } catch (err: any) {
      setState(prev => ({
        ...prev,
        error: err.message || 'Failed to fetch error data',
      }));
    }
  }, [enableErrorTracking]);

  const fetchRecoveryData = useCallback(async () => {
    if (!enableRecoveryTracking) return;
    
    try {
      const [recoveryData, fallbackData] = await Promise.all([
        recoveryApi.getRecoveryCapabilities(),
        recoveryApi.getFallbackStats()
      ]);
      
      setState(prev => ({
        ...prev,
        recovery: recoveryData,
        fallback: fallbackData,
        lastUpdated: new Date(),
      }));
    } catch (err: any) {
      setState(prev => ({
        ...prev,
        error: err.message || 'Failed to fetch recovery data',
      }));
    }
  }, [enableRecoveryTracking]);

  const refreshAll = useCallback(() => {
    setState(prev => ({ ...prev, isLoading: true }));
    fetchAllData();
  }, [fetchAllData]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchAllData();
  }, [fetchAllData]);

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh || refreshInterval <= 0) return;

    const interval = setInterval(fetchAllData, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchAllData]);

  // Computed values
  const getOverallHealth = useCallback(() => {
    const components = Object.values(state.health);
    if (components.length === 0) return { status: 'unknown', score: 0, healthyCount: 0, totalCount: 0 };

    const avgScore = components.reduce((sum, comp) => sum + comp.health_score, 0) / components.length;
    const hasErrors = components.some(comp => comp.status === 'error');
    const hasWarnings = components.some(comp => comp.status === 'warning');
    const healthyCount = components.filter(comp => comp.status === 'healthy').length;

    let status = 'healthy';
    if (hasErrors) status = 'error';
    else if (hasWarnings) status = 'warning';

    return { 
      status, 
      score: Math.round(avgScore), 
      healthyCount, 
      totalCount: components.length 
    };
  }, [state.health]);

  const getOverallPerformance = useCallback(() => {
    const components = Object.values(state.performance);
    if (components.length === 0) return { avgTime: 0, avgSuccessRate: 0, totalRequests: 0 };

    const avgTime = components.reduce((sum, comp) => sum + comp.avg_time_ms, 0) / components.length;
    const avgSuccessRate = components.reduce((sum, comp) => sum + comp.success_rate, 0) / components.length;
    const totalRequests = components.reduce((sum, comp) => sum + comp.total_requests, 0);

    return { avgTime, avgSuccessRate, totalRequests };
  }, [state.performance]);

  const getTotalErrors = useCallback(() => {
    const components = Object.values(state.errors);
    return components.reduce((sum, comp) => sum + comp.total_errors, 0);
  }, [state.errors]);

  const getOverallRecoveryRate = useCallback(() => {
    const components = Object.values(state.recovery);
    if (components.length === 0) return 0;

    const avgRate = components.reduce((sum, comp) => sum + comp.recovery_success_rate, 0) / components.length;
    return avgRate;
  }, [state.recovery]);

  return {
    // State
    ...state,
    
    // Actions
    refreshAll,
    fetchHealthData,
    fetchPerformanceData,
    fetchErrorData,
    fetchRecoveryData,
    clearError,
    
    // Computed values
    overallHealth: getOverallHealth(),
    overallPerformance: getOverallPerformance(),
    totalErrors: getTotalErrors(),
    overallRecoveryRate: getOverallRecoveryRate(),
  };
};
