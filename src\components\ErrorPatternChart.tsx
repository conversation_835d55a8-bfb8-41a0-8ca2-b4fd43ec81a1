import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  AlertTriangle, 
  TrendingUp, 
  RefreshCw,
  XCircle,
  BarChart3,
  AlertCircle,
  Clock
} from 'lucide-react';
import { componentErrorApi } from '@/api/client';
import { ComponentType, ComponentErrorResponse, ComponentError } from '@/types/api';
import { getSourceColor } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface ErrorPatternChartProps {
  refreshInterval?: number;
  showRefreshButton?: boolean;
  maxPatternsPerComponent?: number;
}

const ErrorPatternChart: React.FC<ErrorPatternChartProps> = ({ 
  refreshInterval = 60000, // 1 minute
  showRefreshButton = true,
  maxPatternsPerComponent = 5
}) => {
  const [errorPatterns, setErrorPatterns] = useState<Record<ComponentType, ComponentErrorResponse>>({} as Record<ComponentType, ComponentErrorResponse>);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const { toast } = useToast();

  const fetchErrorPatterns = async () => {
    try {
      setError(null);
      const errorData = await componentErrorApi.getAllComponentErrors();
      setErrorPatterns(errorData);
      setLastUpdated(new Date());
    } catch (err: any) {
      setError(err.message || 'Failed to fetch error patterns');
      toast({
        title: "Error Pattern Fetch Failed",
        description: "Failed to fetch component error patterns",
        variant: "destructive",
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchErrorPatterns();
    
    if (refreshInterval > 0) {
      const interval = setInterval(fetchErrorPatterns, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshInterval]);

  const getComponentDisplayName = (component: ComponentType): string => {
    const displayNames: Record<ComponentType, string> = {
      rule_engine: 'Rule Engine',
      cache: 'Cache',
      llm: 'LLM',
      clarifier: 'Clarifier'
    };
    return displayNames[component] || component;
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const renderErrorPatternCard = (component: ComponentType, errorData: ComponentErrorResponse) => {
    const componentColor = getSourceColor(component);
    const hasErrors = errorData.total_errors > 0;
    
    return (
      <Card key={component} className="transition-all duration-200 hover:shadow-md">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">
              {getComponentDisplayName(component)}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge className={componentColor} variant="outline">
                {component}
              </Badge>
              {hasErrors && (
                <AlertTriangle className="w-4 h-4 text-yellow-500" />
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Error Summary */}
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center p-2 bg-gray-50 rounded">
              <div className="text-lg font-semibold text-gray-900">
                {errorData.total_errors}
              </div>
              <div className="text-xs text-gray-600">Total Errors</div>
            </div>
            <div className="text-center p-2 bg-red-50 rounded">
              <div className="text-lg font-semibold text-red-600">
                {errorData.error_rate.toFixed(2)}%
              </div>
              <div className="text-xs text-red-600">Error Rate</div>
            </div>
          </div>

          {/* Common Patterns */}
          {errorData.common_patterns && errorData.common_patterns.length > 0 ? (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <BarChart3 className="w-4 h-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Common Patterns</span>
              </div>
              <div className="space-y-1">
                {errorData.common_patterns.slice(0, maxPatternsPerComponent).map((pattern, index) => (
                  <div key={index} className="text-xs bg-gray-100 p-2 rounded border-l-2 border-gray-300">
                    {pattern}
                  </div>
                ))}
                {errorData.common_patterns.length > maxPatternsPerComponent && (
                  <div className="text-xs text-gray-500 text-center py-1">
                    +{errorData.common_patterns.length - maxPatternsPerComponent} more patterns
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-4 text-gray-500">
              <AlertCircle className="w-8 h-8 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">No error patterns detected</p>
            </div>
          )}

          {/* Recent Errors */}
          {errorData.recent_errors && errorData.recent_errors.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Recent Errors</span>
              </div>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {errorData.recent_errors.slice(0, 3).map((recentError) => (
                  <div key={recentError.error_id} className="text-xs p-2 rounded border">
                    <div className="flex items-center justify-between mb-1">
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${getSeverityColor(recentError.severity)}`}
                      >
                        {recentError.severity}
                      </Badge>
                      <span className="text-gray-500">
                        {new Date(recentError.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="text-gray-700 font-medium">{recentError.error_type}</div>
                    <div className="text-gray-600 truncate">{recentError.message}</div>
                    {recentError.frequency > 1 && (
                      <div className="text-orange-600 text-xs mt-1">
                        Occurred {recentError.frequency} times
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  const getOverallErrorStats = () => {
    const components = Object.values(errorPatterns);
    if (components.length === 0) return { totalErrors: 0, avgErrorRate: 0, criticalErrors: 0 };

    const totalErrors = components.reduce((sum, comp) => sum + comp.total_errors, 0);
    const avgErrorRate = components.reduce((sum, comp) => sum + comp.error_rate, 0) / components.length;
    const criticalErrors = components.reduce((sum, comp) => {
      return sum + comp.recent_errors.filter(err => err.severity === 'critical').length;
    }, 0);

    return { totalErrors, avgErrorRate, criticalErrors };
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Error Pattern Analysis</h3>
          <Skeleton className="h-9 w-24" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-3">
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-2">
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                </div>
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-16 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const overallStats = getOverallErrorStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Error Pattern Analysis</h3>
          {lastUpdated && (
            <p className="text-sm text-gray-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </p>
          )}
        </div>
        
        {showRefreshButton && (
          <Button
            variant="outline"
            size="sm"
            onClick={fetchErrorPatterns}
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        )}
      </div>

      {/* Overall Error Summary */}
      <Card className="bg-gradient-to-r from-red-50 to-orange-50 border-red-200">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-1">
                <XCircle className="w-4 h-4 text-red-600" />
                <span className="text-sm font-medium text-red-800">Total Errors</span>
              </div>
              <p className="text-2xl font-bold text-red-900">
                {overallStats.totalErrors.toLocaleString()}
              </p>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-1">
                <TrendingUp className="w-4 h-4 text-orange-600" />
                <span className="text-sm font-medium text-orange-800">Avg Error Rate</span>
              </div>
              <p className="text-2xl font-bold text-orange-900">
                {overallStats.avgErrorRate.toFixed(2)}%
              </p>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-1">
                <AlertTriangle className="w-4 h-4 text-red-600" />
                <span className="text-sm font-medium text-red-800">Critical Errors</span>
              </div>
              <p className="text-2xl font-bold text-red-900">
                {overallStats.criticalErrors}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-2">
              <XCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-red-800">Error Pattern Fetch Error</p>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Component Error Pattern Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Object.entries(errorPatterns).map(([component, errorData]) => 
          renderErrorPatternCard(component as ComponentType, errorData)
        )}
      </div>
    </div>
  );
};

export default ErrorPatternChart;
