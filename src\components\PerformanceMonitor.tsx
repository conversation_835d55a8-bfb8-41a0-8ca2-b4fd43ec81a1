import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Activity, 
  Clock, 
  TrendingUp, 
  TrendingDown,
  RefreshCw,
  Zap,
  Target,
  BarChart3
} from 'lucide-react';
import { componentPerformanceApi } from '@/api/client';
import { ComponentPerformanceResponse, ComponentPerformance } from '@/types/api';
import { getSourceColor } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface PerformanceMonitorProps {
  refreshInterval?: number;
  showRefreshButton?: boolean;
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ 
  refreshInterval = 5000, // 5 seconds for performance data
  showRefreshButton = true 
}) => {
  const [componentPerf, setComponentPerf] = useState<ComponentPerformanceResponse>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const { toast } = useToast();

  const fetchComponentPerformance = async () => {
    try {
      setError(null);
      const perfData = await componentPerformanceApi.getComponentsPerformance();
      setComponentPerf(perfData);
      setLastUpdated(new Date());
    } catch (err: any) {
      setError(err.message || 'Failed to fetch performance data');
      toast({
        title: "Performance Fetch Failed",
        description: "Failed to fetch component performance data",
        variant: "destructive",
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchComponentPerformance();
    
    if (refreshInterval > 0) {
      const interval = setInterval(fetchComponentPerformance, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshInterval]);

  const getComponentDisplayName = (component: string): string => {
    const displayNames: Record<string, string> = {
      rule_engine: 'Rule Engine',
      cache: 'Cache',
      llm: 'LLM',
      clarifier: 'Clarifier'
    };
    return displayNames[component] || component;
  };

  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.warning) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 95) return 'text-green-600';
    if (rate >= 90) return 'text-yellow-600';
    return 'text-red-600';
  };

  const renderPerformanceCard = (component: string, metrics: ComponentPerformance) => {
    const componentColor = getSourceColor(component);
    
    return (
      <Card key={component} className="transition-all duration-200 hover:shadow-md">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">
              {getComponentDisplayName(component)}
            </CardTitle>
            <Badge className={componentColor}>
              {component}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Average Response Time */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">Avg Time</span>
            </div>
            <Badge 
              variant="outline" 
              className={getPerformanceColor(metrics.avg_time_ms, { good: 100, warning: 500 })}
            >
              {metrics.avg_time_ms.toFixed(5)}ms
            </Badge>
          </div>

          {/* Success Rate */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">Success Rate</span>
            </div>
            <Badge 
              variant="outline" 
              className={getSuccessRateColor(metrics.success_rate)}
            >
              {metrics.success_rate.toFixed(1)}%
            </Badge>
          </div>

          {/* Error Rate */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <TrendingDown className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">Error Rate</span>
            </div>
            <Badge 
              variant={metrics.error_rate > 5 ? "destructive" : "outline"}
              className="text-xs"
            >
              {metrics.error_rate.toFixed(2)}%
            </Badge>
          </div>

          {/* Requests per Minute */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Activity className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">Req/Min</span>
            </div>
            <Badge variant="outline" className="text-xs">
              {metrics.requests_per_minute.toFixed(0)}
            </Badge>
          </div>

          {/* P95 Response Time */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">P95</span>
            </div>
            <Badge 
              variant="outline" 
              className={getPerformanceColor(metrics.p95_response_time, { good: 200, warning: 1000 })}
            >
              {metrics.p95_response_time.toFixed(2)}ms
            </Badge>
          </div>

          {/* Total Requests */}
          <div className="text-xs text-gray-500 flex items-center justify-between">
            <span>Total Requests:</span>
            <span className="font-medium">{metrics.total_requests.toLocaleString()}</span>
          </div>
        </CardContent>
      </Card>
    );
  };

  const getOverallPerformance = () => {
    const components = Object.values(componentPerf);
    if (components.length === 0) return { avgTime: 0, avgSuccessRate: 0, totalRequests: 0 };

    const avgTime = components.reduce((sum, comp) => sum + comp.avg_time_ms, 0) / components.length;
    const avgSuccessRate = components.reduce((sum, comp) => sum + comp.success_rate, 0) / components.length;
    const totalRequests = components.reduce((sum, comp) => sum + comp.total_requests, 0);

    return { avgTime, avgSuccessRate, totalRequests };
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Performance Monitor</h3>
          <Skeleton className="h-9 w-24" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-3">
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent className="space-y-3">
                {[...Array(5)].map((_, j) => (
                  <div key={j} className="flex justify-between">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-12" />
                  </div>
                ))}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const overallPerf = getOverallPerformance();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Performance Monitor</h3>
          {lastUpdated && (
            <p className="text-sm text-gray-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </p>
          )}
        </div>
        
        {showRefreshButton && (
          <Button
            variant="outline"
            size="sm"
            onClick={fetchComponentPerformance}
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        )}
      </div>

      {/* Overall Performance Summary */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-1">
                <Clock className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">Avg Response Time</span>
              </div>
              <p className="text-2xl font-bold text-blue-900">
                {overallPerf.avgTime.toFixed(2)}ms
              </p>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-1">
                <TrendingUp className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">Avg Success Rate</span>
              </div>
              <p className="text-2xl font-bold text-green-900">
                {overallPerf.avgSuccessRate.toFixed(1)}%
              </p>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-1">
                <Zap className="w-4 h-4 text-purple-600" />
                <span className="text-sm font-medium text-purple-800">Total Requests</span>
              </div>
              <p className="text-2xl font-bold text-purple-900">
                {overallPerf.totalRequests.toLocaleString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-2">
              <TrendingDown className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-red-800">Performance Data Error</p>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Component Performance Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Object.entries(componentPerf).map(([component, metrics]) => 
          renderPerformanceCard(component, metrics)
        )}
      </div>
    </div>
  );
};

export default PerformanceMonitor;
