# 🔧 Backend Implementation Guide - Component Monitoring System

## 📋 **Overview**

This guide provides **step-by-step instructions** to implement all the backend endpoints required for the Component Monitoring System to work. You'll need to add these endpoints to your FastAPI backend.

## 🎯 **What You Need to Implement**

The frontend is currently trying to call these endpoints that don't exist yet:

```
❌ GET /api/intent/health/components
❌ GET /api/intent/health/{component}
❌ GET /api/intent/performance/components
❌ GET /api/intent/performance/{component}
❌ GET /api/intent/errors/rule_engine
❌ GET /api/intent/errors/cache
❌ GET /api/intent/errors/llm
❌ GET /api/intent/errors/clarifier
❌ GET /api/intent/fallback/stats
❌ GET /api/intent/recovery/capabilities
❌ POST /api/intent/debug/component
```

## 🚀 **Step-by-Step Implementation**

### **Step 1: Create Data Models**

First, create Pydantic models for the API responses. Add this to your backend:

```python
# models/monitoring.py (create this file)
from pydantic import BaseModel
from typing import Dict, List, Optional, Literal
from datetime import datetime

class ComponentHealth(BaseModel):
    status: Literal["healthy", "warning", "error"]
    health_score: int  # 0-100
    recent_errors: int
    last_check: str  # ISO datetime string
    uptime_percentage: float
    response_time_ms: float

class ComponentPerformance(BaseModel):
    avg_time_ms: float
    success_rate: float
    error_rate: float
    total_requests: int
    requests_per_minute: int
    p95_response_time: float
    p99_response_time: float

class ComponentError(BaseModel):
    error_id: str
    component: str
    error_type: str
    message: str
    timestamp: str
    frequency: int
    severity: Literal["low", "medium", "high", "critical"]
    context: Optional[Dict] = None

class ComponentErrorResponse(BaseModel):
    component: str
    total_errors: int
    recent_errors: List[ComponentError]
    common_patterns: List[str]
    error_rate: float

class RecoveryStats(BaseModel):
    recovery_attempts: int
    recovery_successes: int
    recovery_success_rate: float
    avg_recovery_time_ms: float
    last_recovery_attempt: Optional[str] = None

class FallbackStats(BaseModel):
    total_fallbacks: int
    fallback_rate: float
    fallback_success_rate: float
    common_fallback_triggers: List[str]

class ComponentDebugRequest(BaseModel):
    component: str
    message: str
    tenant_id: Optional[str] = None
    debug_level: Literal["basic", "detailed", "verbose"] = "basic"

class ComponentDebugResponse(BaseModel):
    component: str
    status: Literal["success", "error"]
    processing_time_ms: float
    debug_info: Dict
    errors: Optional[List[str]] = []
    warnings: Optional[List[str]] = []
```

### **Step 2: Create Monitoring Service**

Create a service class to handle monitoring logic:

```python
# services/monitoring_service.py (create this file)
import time
from datetime import datetime, timezone
from typing import Dict, List
from models.monitoring import *

class MonitoringService:
    def __init__(self):
        # Initialize with your actual components
        self.components = ["rule_engine", "cache", "llm", "clarifier"]
        
    def get_component_health(self, component: str = None) -> Dict:
        """Get health status for components"""
        if component:
            return self._get_single_component_health(component)
        
        # Return all components health
        health_data = {}
        for comp in self.components:
            health_data[comp] = self._get_single_component_health(comp)
        
        return health_data
    
    def _get_single_component_health(self, component: str) -> ComponentHealth:
        """Get health for a specific component"""
        # TODO: Replace with actual health checks
        
        if component == "rule_engine":
            return ComponentHealth(
                status="healthy",
                health_score=95,
                recent_errors=0,
                last_check=datetime.now(timezone.utc).isoformat(),
                uptime_percentage=99.5,
                response_time_ms=2.5
            )
        elif component == "cache":
            return ComponentHealth(
                status="healthy",
                health_score=98,
                recent_errors=0,
                last_check=datetime.now(timezone.utc).isoformat(),
                uptime_percentage=99.8,
                response_time_ms=1.1
            )
        elif component == "llm":
            return ComponentHealth(
                status="warning",
                health_score=85,
                recent_errors=2,
                last_check=datetime.now(timezone.utc).isoformat(),
                uptime_percentage=97.8,
                response_time_ms=1250.0
            )
        elif component == "clarifier":
            return ComponentHealth(
                status="healthy",
                health_score=92,
                recent_errors=0,
                last_check=datetime.now(timezone.utc).isoformat(),
                uptime_percentage=98.5,
                response_time_ms=45.0
            )
        else:
            # Default for unknown components
            return ComponentHealth(
                status="error",
                health_score=0,
                recent_errors=999,
                last_check=datetime.now(timezone.utc).isoformat(),
                uptime_percentage=0.0,
                response_time_ms=0.0
            )
    
    def get_component_performance(self, component: str = None) -> Dict:
        """Get performance metrics for components"""
        if component:
            return {component: self._get_single_component_performance(component)}
        
        # Return all components performance
        perf_data = {}
        for comp in self.components:
            perf_data[comp] = self._get_single_component_performance(comp)
        
        return perf_data
    
    def _get_single_component_performance(self, component: str) -> ComponentPerformance:
        """Get performance for a specific component"""
        # TODO: Replace with actual performance metrics
        
        performance_data = {
            "rule_engine": ComponentPerformance(
                avg_time_ms=2.5,
                success_rate=99.2,
                error_rate=0.8,
                total_requests=15420,
                requests_per_minute=45,
                p95_response_time=4.2,
                p99_response_time=8.1
            ),
            "cache": ComponentPerformance(
                avg_time_ms=1.1,
                success_rate=99.8,
                error_rate=0.2,
                total_requests=12890,
                requests_per_minute=38,
                p95_response_time=2.1,
                p99_response_time=3.5
            ),
            "llm": ComponentPerformance(
                avg_time_ms=1250.0,
                success_rate=97.8,
                error_rate=2.2,
                total_requests=8945,
                requests_per_minute=25,
                p95_response_time=2100.0,
                p99_response_time=3500.0
            ),
            "clarifier": ComponentPerformance(
                avg_time_ms=45.0,
                success_rate=98.5,
                error_rate=1.5,
                total_requests=3421,
                requests_per_minute=12,
                p95_response_time=85.0,
                p99_response_time=150.0
            )
        }
        
        return performance_data.get(component, ComponentPerformance(
            avg_time_ms=0.0,
            success_rate=0.0,
            error_rate=100.0,
            total_requests=0,
            requests_per_minute=0,
            p95_response_time=0.0,
            p99_response_time=0.0
        ))
    
    def get_component_errors(self, component: str) -> ComponentErrorResponse:
        """Get error data for a specific component"""
        # TODO: Replace with actual error tracking
        
        sample_errors = [
            ComponentError(
                error_id=f"err_{component}_001",
                component=component,
                error_type="TimeoutError",
                message=f"{component} request timeout after 30 seconds",
                timestamp=datetime.now(timezone.utc).isoformat(),
                frequency=3,
                severity="medium",
                context={"timeout_ms": 30000}
            ),
            ComponentError(
                error_id=f"err_{component}_002",
                component=component,
                error_type="ConnectionError",
                message=f"Failed to connect to {component} service",
                timestamp=datetime.now(timezone.utc).isoformat(),
                frequency=1,
                severity="high",
                context={"retry_count": 3}
            )
        ]
        
        return ComponentErrorResponse(
            component=component,
            total_errors=len(sample_errors),
            recent_errors=sample_errors,
            common_patterns=[
                f"{component} timeout errors during peak hours",
                f"Connection refused to {component} service",
                f"{component} rate limit exceeded"
            ],
            error_rate=2.1
        )
    
    def get_fallback_stats(self) -> FallbackStats:
        """Get fallback statistics"""
        # TODO: Replace with actual fallback tracking
        
        return FallbackStats(
            total_fallbacks=45,
            fallback_rate=2.3,
            fallback_success_rate=89.5,
            common_fallback_triggers=[
                "LLM timeout",
                "Cache miss with high load",
                "Rule engine pattern mismatch",
                "Clarifier service unavailable"
            ]
        )
    
    def get_recovery_capabilities(self) -> Dict[str, RecoveryStats]:
        """Get recovery statistics for all components"""
        # TODO: Replace with actual recovery tracking
        
        return {
            "rule_engine": RecoveryStats(
                recovery_attempts=12,
                recovery_successes=11,
                recovery_success_rate=91.7,
                avg_recovery_time_ms=150.5,
                last_recovery_attempt=datetime.now(timezone.utc).isoformat()
            ),
            "cache": RecoveryStats(
                recovery_attempts=8,
                recovery_successes=8,
                recovery_success_rate=100.0,
                avg_recovery_time_ms=50.2,
                last_recovery_attempt=datetime.now(timezone.utc).isoformat()
            ),
            "llm": RecoveryStats(
                recovery_attempts=25,
                recovery_successes=20,
                recovery_success_rate=80.0,
                avg_recovery_time_ms=2500.0,
                last_recovery_attempt=datetime.now(timezone.utc).isoformat()
            ),
            "clarifier": RecoveryStats(
                recovery_attempts=6,
                recovery_successes=5,
                recovery_success_rate=83.3,
                avg_recovery_time_ms=300.0,
                last_recovery_attempt=datetime.now(timezone.utc).isoformat()
            )
        }
    
    def debug_component(self, request: ComponentDebugRequest) -> ComponentDebugResponse:
        """Debug a specific component"""
        start_time = time.time()
        
        # TODO: Replace with actual component debugging logic
        
        # Simulate processing time
        processing_time = (time.time() - start_time) * 1000
        
        debug_info = {
            "input_message": request.message,
            "debug_level": request.debug_level,
            "component_state": "active",
            "memory_usage": "45MB",
            "cpu_usage": "12%",
            "last_activity": datetime.now(timezone.utc).isoformat(),
            "configuration": {
                "timeout_ms": 30000,
                "retry_count": 3,
                "cache_enabled": True
            }
        }
        
        if request.debug_level == "detailed":
            debug_info.update({
                "internal_state": {
                    "queue_size": 5,
                    "active_connections": 12,
                    "pending_requests": 3
                },
                "performance_metrics": {
                    "avg_response_time": 125.5,
                    "requests_per_second": 15.2
                }
            })
        
        if request.debug_level == "verbose":
            debug_info.update({
                "full_trace": [
                    "Component initialized",
                    "Input validation passed",
                    "Processing started",
                    "External API called",
                    "Response received",
                    "Processing completed"
                ],
                "memory_breakdown": {
                    "heap_used": "32MB",
                    "heap_total": "64MB",
                    "external": "8MB"
                }
            })
        
        return ComponentDebugResponse(
            component=request.component,
            status="success",
            processing_time_ms=processing_time,
            debug_info=debug_info,
            errors=[],
            warnings=["Component running at 85% capacity"] if request.component == "llm" else []
        )
```

### **Step 3: Create API Endpoints**

Add these endpoints to your FastAPI application:

```python
# routes/monitoring.py (create this file)
from fastapi import APIRouter, HTTPException, Header
from typing import Optional
from services.monitoring_service import MonitoringService
from models.monitoring import *

router = APIRouter(prefix="/api/intent", tags=["monitoring"])
monitoring_service = MonitoringService()

# Component Health Endpoints
@router.get("/health/components")
async def get_components_health(
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Get health status for all components"""
    try:
        health_data = monitoring_service.get_component_health()
        return health_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get component health: {str(e)}")

@router.get("/health/{component}")
async def get_component_health(
    component: str,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Get health status for a specific component"""
    try:
        if component not in ["rule_engine", "cache", "llm", "clarifier"]:
            raise HTTPException(status_code=404, detail=f"Component '{component}' not found")

        health_data = monitoring_service.get_component_health(component)
        return health_data
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get component health: {str(e)}")

# Performance Monitoring Endpoints
@router.get("/performance/components")
async def get_components_performance(
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Get performance metrics for all components"""
    try:
        performance_data = monitoring_service.get_component_performance()
        return performance_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get component performance: {str(e)}")

@router.get("/performance/{component}")
async def get_component_performance(
    component: str,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Get performance metrics for a specific component"""
    try:
        if component not in ["rule_engine", "cache", "llm", "clarifier"]:
            raise HTTPException(status_code=404, detail=f"Component '{component}' not found")

        performance_data = monitoring_service.get_component_performance(component)
        return performance_data
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get component performance: {str(e)}")

# Error Tracking Endpoints
@router.get("/errors/{component}")
async def get_component_errors(
    component: str,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Get error data for a specific component"""
    try:
        if component not in ["rule_engine", "cache", "llm", "clarifier"]:
            raise HTTPException(status_code=404, detail=f"Component '{component}' not found")

        error_data = monitoring_service.get_component_errors(component)
        return error_data
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get component errors: {str(e)}")

# Recovery and Fallback Endpoints
@router.get("/fallback/stats")
async def get_fallback_stats(
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Get fallback statistics"""
    try:
        fallback_data = monitoring_service.get_fallback_stats()
        return fallback_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get fallback stats: {str(e)}")

@router.get("/recovery/capabilities")
async def get_recovery_capabilities(
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Get recovery capabilities and statistics"""
    try:
        recovery_data = monitoring_service.get_recovery_capabilities()
        return recovery_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get recovery capabilities: {str(e)}")

# Component Debugging Endpoint
@router.post("/debug/component")
async def debug_component(
    request: ComponentDebugRequest,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Debug a specific component with a test message"""
    try:
        if request.component not in ["rule_engine", "cache", "llm", "clarifier"]:
            raise HTTPException(status_code=404, detail=f"Component '{request.component}' not found")

        debug_result = monitoring_service.debug_component(request)
        return debug_result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to debug component: {str(e)}")
```

### **Step 4: Register Routes in Main App**

Add the monitoring routes to your main FastAPI application:

```python
# main.py (update your existing file)
from fastapi import FastAPI
from routes.monitoring import router as monitoring_router

app = FastAPI()

# Add your existing routes here...

# Add the new monitoring routes
app.include_router(monitoring_router)

# Your existing code...
```

### **Step 5: Update Requirements**

Make sure you have the required dependencies:

```txt
# requirements.txt (add these if not already present)
fastapi>=0.104.0
pydantic>=2.0.0
uvicorn>=0.24.0
```

### **Step 6: Test the Endpoints**

Start your FastAPI server and test the endpoints:

```bash
# Start your backend server
uvicorn main:app --reload --port 8000

# Test the endpoints
curl -H "X-Tenant-ID: 0191fc1a-ae52-7796-9d29-5691aba7f284" \
     http://localhost:8000/api/intent/health/components

curl -H "X-Tenant-ID: 0191fc1a-ae52-7796-9d29-5691aba7f284" \
     http://localhost:8000/api/intent/performance/components

curl -H "X-Tenant-ID: 0191fc1a-ae52-7796-9d29-5691aba7f284" \
     http://localhost:8000/api/intent/errors/rule_engine

curl -H "X-Tenant-ID: 0191fc1a-ae52-7796-9d29-5691aba7f284" \
     http://localhost:8000/api/intent/fallback/stats

curl -H "X-Tenant-ID: 0191fc1a-ae52-7796-9d29-5691aba7f284" \
     http://localhost:8000/api/intent/recovery/capabilities

# Test debug endpoint
curl -X POST \
     -H "Content-Type: application/json" \
     -H "X-Tenant-ID: 0191fc1a-ae52-7796-9d29-5691aba7f284" \
     -d '{"component": "rule_engine", "message": "test message", "debug_level": "basic"}' \
     http://localhost:8000/api/intent/debug/component
```

## 🔧 **Integration with Existing Components**

### **Step 7: Connect to Real Components**

Replace the mock data in `MonitoringService` with real component monitoring:

```python
# Example: Connect to your actual rule engine
def _get_rule_engine_health(self) -> ComponentHealth:
    try:
        # Get actual health from your rule engine
        rule_engine = self.orchestrator.rule_engine

        # Check if rule engine is responsive
        start_time = time.time()
        rule_count = len(rule_engine.rules)
        response_time = (time.time() - start_time) * 1000

        # Get error count from your error tracking
        recent_errors = self._get_recent_errors("rule_engine")

        # Calculate health score based on performance
        health_score = self._calculate_health_score(
            response_time=response_time,
            error_count=recent_errors,
            uptime=self._get_uptime("rule_engine")
        )

        return ComponentHealth(
            status="healthy" if health_score > 80 else "warning" if health_score > 50 else "error",
            health_score=health_score,
            recent_errors=recent_errors,
            last_check=datetime.now(timezone.utc).isoformat(),
            uptime_percentage=self._get_uptime("rule_engine"),
            response_time_ms=response_time
        )
    except Exception as e:
        return ComponentHealth(
            status="error",
            health_score=0,
            recent_errors=999,
            last_check=datetime.now(timezone.utc).isoformat(),
            uptime_percentage=0.0,
            response_time_ms=0.0
        )
```

### **Step 8: Add Real Performance Tracking**

```python
# Example: Track real performance metrics
def _get_rule_engine_performance(self) -> ComponentPerformance:
    try:
        # Get performance stats from your orchestrator
        stats = self.orchestrator.rule_engine.get_performance_stats()

        return ComponentPerformance(
            avg_time_ms=stats.get("avg_time_ms", 0.0),
            success_rate=stats.get("success_rate", 0.0),
            error_rate=stats.get("error_rate", 0.0),
            total_requests=stats.get("total_requests", 0),
            requests_per_minute=stats.get("requests_per_minute", 0),
            p95_response_time=stats.get("p95_response_time", 0.0),
            p99_response_time=stats.get("p99_response_time", 0.0)
        )
    except Exception:
        return ComponentPerformance(
            avg_time_ms=0.0,
            success_rate=0.0,
            error_rate=100.0,
            total_requests=0,
            requests_per_minute=0,
            p95_response_time=0.0,
            p99_response_time=0.0
        )
```

## 🚨 **Important Notes**

### **Security Considerations**
- All endpoints require `X-Tenant-ID` header
- Add authentication/authorization as needed
- Validate component names to prevent injection
- Rate limit monitoring endpoints

### **Performance Considerations**
- Cache monitoring data for frequently accessed endpoints
- Use background tasks for expensive health checks
- Implement circuit breakers for external dependencies
- Add pagination for large error lists

### **Error Handling**
- All endpoints have proper error handling
- Return appropriate HTTP status codes
- Log errors for debugging
- Provide meaningful error messages

## ✅ **Verification Checklist**

After implementing these changes:

- [ ] All 11 endpoints are implemented
- [ ] FastAPI server starts without errors
- [ ] Endpoints return proper JSON responses
- [ ] X-Tenant-ID header is handled correctly
- [ ] Error handling works for invalid components
- [ ] Frontend can successfully call all endpoints
- [ ] Health, Performance, Errors, Recovery, and Debugger tabs work

## 🎯 **Expected Results**

Once implemented, your frontend will show:

- ✅ **Health Tab**: Real-time component health with color-coded status
- ✅ **Performance Tab**: Component performance metrics with charts
- ✅ **Errors Tab**: Error patterns and recent errors by component
- ✅ **Recovery Tab**: Recovery statistics and fallback data
- ✅ **Debugger Tab**: Component debugging with test messages

## 🔄 **Next Steps**

1. **Implement the backend endpoints** using this guide
2. **Test each endpoint** individually
3. **Verify frontend integration** works
4. **Replace mock data** with real component monitoring
5. **Add authentication** and security measures
6. **Optimize performance** for production use

This implementation will give you a fully functional component monitoring system! 🚀
```
